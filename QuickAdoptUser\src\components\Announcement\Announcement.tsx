import { Box, Typography } from "@mui/material";
import React, { useState, useEffect, useMemo } from "react";
import TourStep from "./TourSteps/TourSteps";
import AnnouncementPopup from "./AnnouncementPopUp";
//import { guideData } from "../../service/AnnouncementService";
import { IReponse, useFetch } from "../../hooks/useFetch";
import { POST } from "../../service/APIService";
import { browser, trackUserEngagement, userData,version } from "../UserEngagement/userEngagementTracking";
import { useUrlChange } from "../../hooks/useUrlChange";
//import { formatDateTime } from "./Timeconversion";

const Announcement = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(document.body);
  const [currentStep, setCurrentStep] = useState(0);
  const [isAnnouncementOpen, setAnnouncementOpen] = useState(false);
  const [scrollPercentage, setScrollPercentage] = useState(0);

  // Use our custom URL change detection hook
  const currentUrl = useUrlChange();
  let initialTime : any;
  const [response, setResponse] = useState<IReponse>({
		data: [],
		loading: false,
		error: {
			message: "",
			isError: false,
		},
	});
  // const [response] = useFetch({
  //   // url: "/Guide/GetGuideDetails?guideId=********-*********-0097902d-ecb8-4333-a70a-ba151cc641a6",
  //   url: `/EndUserGuide/GetGuideListByTargetUrl?targetUrl=${currentUrl}`,
  // });
  let accountId = localStorage.getItem('AccountId') ?? "";



  const [guideDetails] = useFetch({
    url: `/EndUserGuide/GetGuideListByTargetUrl?targetUrl=${encodeURIComponent(currentUrl)}&accountId=${accountId}`,
    dependencies: [currentUrl], // Re-fetch when currentUrl changes
    timeout: 30000, // 30 second soft timeout (logs warning but allows slow APIs)
    hardTimeout: 120000 // 2 minute hard timeout for very slow APIs
  });

  // Add logging to track API calls and responses
  // useEffect(() => {
  //   console.log('🔄 Announcement: API call triggered for URL:', currentUrl);
  //   console.log('🔄 Announcement: Account ID:', accountId);
  //   console.log('🔄 Announcement: Full API URL:', `/EndUserGuide/GetGuideListByTargetUrl?targetUrl=${encodeURIComponent(currentUrl)}&accountId=${accountId}`);
  // }, [currentUrl, accountId]);

  // useEffect(() => {
  //   if (guideDetails) {
  //     console.log('🔄 Announcement: API response received:', {
  //       loading: guideDetails.loading,
  //       dataLength: guideDetails.data?.length || 0,
  //       error: guideDetails.error,
  //       url: currentUrl
  //     });
  //   }
  // }, [guideDetails, currentUrl]);
  const [hasViewed, setHasViewed] = useState(false);
  const [isDataReady, setIsDataReady] = useState(false);

  useEffect(() => {
    if (guideDetails && guideDetails.data && guideDetails.data.length > 0) {
      setResponse(prev => ({
        ...prev,
        data: guideDetails.data,
        loading: guideDetails.loading,
        error: guideDetails.error,
      }));
      setIsDataReady(true);
    } else if (guideDetails && !guideDetails.loading) {
      // API call completed but no data - reset state
      setResponse(prev => ({
        ...prev,
        data: [],
        loading: false,
        error: guideDetails.error,
      }));
      setIsDataReady(false);
    }
  }, [guideDetails, currentUrl]);

  // Reset states when URL changes - IMMEDIATELY clear old announcements
  useEffect(() => {
    //console.log('🔄 Announcement: URL changed, clearing old announcements');
    setIsDataReady(false);
    setHasViewed(false);
    setAnnouncementOpen(false); // Hide current announcement immediately
    setCurrentStep(0);
    setAnchorEl(null); // Clear anchor element

    // Clear the response data immediately to prevent showing old announcements
    setResponse({
      data: [],
      loading: true, // Set loading to true while new API call is in progress
      error: {
        message: "",
        isError: false,
      },
    });
  }, [currentUrl]);


  const announcements = useMemo(() => {
    // Only return announcements if we have fresh data for the current URL
    if (!isDataReady || response.loading) {
      return {};
    }

    return (
      (response?.data as any[]).find(
        (item) => (item.GuideType === "Announcement" && item.GuideStep?.length > 0) // Removing Equals condition as we have page targets
      ) || {}
    );
  }, [currentUrl, response, isDataReady]);


  const data = announcements ? announcements : [];
  const stepData = data && data?.GuideStep?.[currentStep]?.StepTitle;
  initialTime = Date.now();
  useEffect(() => {
    if (!hasViewed && data?.GuideStep) {
      let tourClosed = localStorage.getItem('closedGuides_/');
      let announcementExists = false;
  if(tourClosed!=null){
    const guideArray: Guide[] = JSON.parse(tourClosed);
     announcementExists = guideArray.some((x) => x.GuideType === "Announcement" && x.GuideId ===data?.GuideId);
  }
  if(!announcementExists)
      trackUserEngagement("guide-view", userData, data, browser,version,"Announcementview",stepData, 0, 0);
      setHasViewed(true);
    }
  });
  const totalSteps = data ? data.GuideStep?.length : "";
  const showAfter = data?.GuideStep?.[0]?.Advanced?.ShowAfter || "0s";
  const showDelay = parseInt(showAfter) * 1000;
  const [thresholdValue, setThresholdValue] = useState(0);
  useEffect(() => {
    const apiThreshold = data?.GuideStep?.[currentStep]?.Advanced?.OnScrollDelay || "";
    setThresholdValue(apiThreshold);
  }, [data]);


  const onScrollDelay = data?.GuideStep?.[currentStep]?.Advanced?.OnScrollDelay || "";

  // Enhanced announcement display logic with better timing control
  useEffect(() => {
    // Only proceed if we have data and it's ready
    if (!isDataReady || !data?.GuideStep || !data.GuideStep.length) {
      return;
    }

    // Check if announcement should be shown based on scroll percentage
    const scrollThreshold = parseFloat(onScrollDelay) || 0;
    if (scrollPercentage < scrollThreshold) {
      return;
    }

    // Check if announcement is already closed by user
    const storedGuides = JSON.parse(localStorage.getItem('closedGuides_/') || '[]');
    const isGuideClosed = storedGuides.some((guide: any) =>
      guide.GuideId === data.GuideId && guide.DontShowAgain === true
    );

    if (isGuideClosed) {
      return;
    }

    // Date validation logic
    const currentDate = new Date();
    const publishDate = data?.PublishDate ? new Date(data.PublishDate) : null;
    const unpublishDate = data?.UnPublishDate ? new Date(data.UnPublishDate) : null;

    let shouldShow = false;

    if (publishDate && unpublishDate) {
      // Both dates specified - check if current date is within range
      shouldShow = currentDate >= publishDate && currentDate <= unpublishDate;
    } else if (publishDate && !unpublishDate) {
      // Only publish date specified - check if current date is after publish date
      shouldShow = currentDate >= publishDate;
    } else if (!publishDate && !unpublishDate) {
      // No date restrictions - always show
      shouldShow = true;
    }

    if (shouldShow) {
      const timer = setTimeout(() => {
        setAnnouncementOpen(true);
      }, showDelay);

      return () => clearTimeout(timer);
    }
  }, [isDataReady, data?.GuideStep, data?.GuideId, data?.PublishDate, data?.UnPublishDate, scrollPercentage, onScrollDelay, showDelay]);



  // Enhanced scroll tracking for announcement display
  useEffect(() => {
    const handleScroll = () => {
      const documentHeight = document.documentElement.scrollHeight;

      // Prevent division by zero
      if (documentHeight <= window.innerHeight) {
        setScrollPercentage(100);
        return;
      }

      const scrolled = (window.scrollY / (documentHeight - window.innerHeight)) * 100;
      setScrollPercentage(Math.min(100, Math.max(0, scrolled)));
    };

    // Initial scroll calculation
    handleScroll();

    // Add scroll listener
    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [currentUrl]); // Reset scroll tracking when URL changes


  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
        }
  };

  const handleContinue = () => {
    if (currentStep < totalSteps - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      setAnchorEl(null);
    }
  };

  const handleRestart = () => {
    // Navigate back to Step 1 of the workflow
    setCurrentStep(0); // In Announcement component, steps are 0-indexed
    // Make sure the announcement is still open
    setAnnouncementOpen(true);
  };

  const handleDontShowAgain = () => {
    setAnchorEl(null);
  };

  const handleStepChange = (step: number) => {
    setCurrentStep(step);
  };

  const progress = ((currentStep + 1) / totalSteps) * 100;
  // Define the type for the guide objects
interface Guide {
  GuideId: string;
  DontShowAgain: boolean;
  GuideType: string;
}

useEffect(() => {
  const handleStorageChange = () => {
    if (!data?.GuideId) return; // Guard against undefined data

    const storedGuides: Guide[] = JSON.parse(localStorage.getItem('closedGuides_/') || '[]');
    const isGuideClosed = storedGuides.some((guide) => guide.GuideId === data.GuideId && guide.DontShowAgain === true);
    if (isDataReady && response && data?.GuideStep?.length > 0 && isGuideClosed) {
      setAnchorEl(null);
      setAnnouncementOpen(false);
    } else if (isDataReady && data?.GuideStep?.length > 0) {
      setAnchorEl(document.body);
    }
  };

  handleStorageChange();
  window.addEventListener('storage', handleStorageChange);
  return () => {
    window.removeEventListener('storage', handleStorageChange);
  };
}, [isDataReady, response, data?.GuideId, data?.GuideStep?.length]);


  const handleClose = () => {
    let timeDiff = Date.now()- initialTime;
    timeDiff = timeDiff/1000;
    trackUserEngagement("guide-view", userData, data, browser,version,"Announcement-Close",stepData,0,timeDiff);
  const storedGuides: Guide[] = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");
  setAnchorEl(null);
  var isAnnCloseSaved = storedGuides.some(
    (guide) => guide.GuideId === data.GuideId && guide.DontShowAgain === true && guide?.GuideType.toLowerCase() === 'announcement'
    );
    if (!isAnnCloseSaved)
    {
        const updatedGuides = [
            ...storedGuides,
            {
                GuideId: data.GuideId,
                DontShowAgain: true,
                GuideType: data.GuideType
            },
        ];
        localStorage.setItem("closedGuides_/", JSON.stringify(updatedGuides));
    }

};


  const handleCloseAnnouncement = async () => {
    const newData = { ...response.data.GuideDetails, Visited: true, VisitedDate: new Date().toISOString() };
    try {
      const res = await POST("/Guide/Updateguide", newData);
      setAnchorEl(null);
    } catch (error) {

    }
  };
  const isBase64 = (str: string) => {
    return str.startsWith("data:image/");
  };
  const finalImageUrl =
  announcements.GuideStep?.[currentStep-1]?.ImageProperties?.map((imgProp:any) =>
    imgProp.CustomImage.map((uploadedImage:any) => uploadedImage.Url)
  ) || [];

const previousButtonLabel =
  announcements.GuideStep?.[currentStep]?.ButtonSection?.[0]?.CustomButtons?.[0]?.ButtonName || "Previous";

const continueButtonLabel =
  announcements.GuideStep?.[currentStep]?.ButtonSection?.[0]?.CustomButtons?.[1]?.ButtonName || "Continue";


  const imageUrl = data?.GuideStep?.[currentStep]?.ImageProperties?.CustomImage?.[currentStep]?.Url || "";


  const videoUrl = data?.GuideStep?.[currentStep]?.VideoEmbedCode;
  const textFieldProperties = data?.GuideStep?.[currentStep]?.TextFieldProperties;
  const imageProperties = data?.GuideStep?.[currentStep]?.ImageProperties;
  const modalProperties = data?.GuideStep?.[currentStep]?.Modal;
  const canvasProperties = data?.GuideStep?.[currentStep]?.Canvas || {};
  const html_Snippet = data.GuideStep?.[currentStep]?.HtmlSnippet;
  const customButtons = data?.GuideStep?.[currentStep]?.CustomButton || [];
    const previousButtonStyles = data?.GuideStep?.[currentStep]?.CustomButton?.[0]?.ButtonProperties;
    const continueButtonStyles = data?.GuideStep?.[currentStep]?.CustomButton?.[0]?.ButtonProperties;
  const Overlayvalue = data?.GuideStep?.[currentStep]?.Overlay;
  const title = data?.GuideStep?.[currentStep]?.StepTitle;
  return (
    <div  >

      {isAnnouncementOpen && (

        <Box sx={{ zIndex: 1000}}>
          <AnnouncementPopup
            rectData=""
            rectDataLeft=""
            data={data}
            anchorEl={anchorEl}
            onClose={handleClose}
            onPrevious={handlePrevious}
            onContinue={handleContinue}
            onRestart={handleRestart}
            onDontShowAgain={handleDontShowAgain}
            title={data?.GuideStep?.[currentStep]?.StepTitle}
            text={
              data?.GuideStep?.[currentStep]?.TextFieldProperties
                ?.map((field:any) => field.Text)
                .filter((text:any) => text) // Remove undefined or null values
                .join(" ") || "" // Combine into a single string with spaces
            }
                        imageUrl={finalImageUrl || ""}
            videoUrl={videoUrl}
            previousButtonLabel={previousButtonLabel}
            continueButtonLabel={continueButtonLabel}
            currentStep={currentStep+1}
            totalSteps={totalSteps}
            progress={progress}
            textFieldProperties={textFieldProperties}
            imageProperties={imageProperties}
            customButton={
              data?.GuideStep?.[currentStep]?.ButtonSection
                  ?.map((section: any) =>
                      section.CustomButtons.map((button: any) => ({
                          ...button,
                          ContainerId: section.Id, // Attach the container ID for grouping
                      }))
                  )
                  ?.reduce((acc: any[], curr: any[]) => acc.concat(curr), []) || [] // Flatten the array
          }
            modalProperties={modalProperties}
            canvasProperties={canvasProperties}
            htmlSnippet={html_Snippet}
            previousButtonStyles={previousButtonStyles}
            continueButtonStyles={continueButtonStyles}
            OverlayValue={Overlayvalue}
            hotspotProperties=""
            selectedOption={data?.GuideStep?.[currentStep]?.Tooltip.ProgressTemplate}
            enableProgress={data?.GuideStep?.[currentStep]?.Tooltip.EnableProgress}
          />
        </Box>
      )}

      {/* Tour Steps */}
      {/* <Box  sx={{ zIndex: 1000 }}>
        <TourStep
          guideSteps={data.GuideStep}
          currentStep={currentStep}
          totalSteps={totalSteps}
          onStepChange={handleStepChange}
        />
      </Box> */}
    </div>
  );
};

export default Announcement;