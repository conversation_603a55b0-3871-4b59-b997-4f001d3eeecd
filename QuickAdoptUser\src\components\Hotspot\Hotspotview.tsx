import React, { useState, useEffect } from "react";
import { IReponse, useFetch } from "../../hooks/useFetch";
import Hotspot from "./Hotspot";
import { browser,  trackUserEngagement, userData, version } from "../UserEngagement/userEngagementTracking";
import { useUrlChange } from "../../hooks/useUrlChange";
const Hotspotview = () => {

  interface Guide {
    GuideId: string;
    DontShowAgain: boolean;
    GuideType: string;
  }
  let isGuideClosed: boolean = false;
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>();
  const [currentStep, setCurrentStep] = useState(0);
  const [popupVisible, setPopupVisible] = useState(false);
  const [popupVisibility, setPopupVisibility] = useState<Record<string, boolean>>({});
  const [showHotspot, setShowHotspot] = useState(false);

  let initialTime = Date.now();

  // Use our custom URL change detection hook
  const currentUrl = useUrlChange();

  const [guides, setGuides] = useState<IReponse>({
    data: [],
    loading: false,
    error: {
      message: "",
      isError: false,
    },
  });
  let accountId = localStorage.getItem('AccountId') ?? "";
  const [guideDetails] = useFetch({
    url: `/EndUserGuide/GetGuideListByTargetUrl?targetUrl=${encodeURIComponent(currentUrl)}&accountId=${accountId}`,
    dependencies: [currentUrl], // Re-fetch when currentUrl changes
    timeout: 30000, // 30 second soft timeout (logs warning but allows slow APIs)
    hardTimeout: 120000 // 2 minute hard timeout for very slow APIs
    });

  // Reset hotspot state when URL changes - clear old hotspots immediately
  useEffect(() => {
    console.log('🔄 Hotspotview: URL changed, clearing old hotspots');
    setCurrentStep(0);
    setPopupVisible(false);
    setPopupVisibility({});
    setGuides({
      data: [],
      loading: true,
      error: {
        message: "",
        isError: false,
      },
    });
  }, [currentUrl]);
  useEffect(() => {
    if (guideDetails?.data?.length > 0) {
      const initialVisibility = guideDetails.data.reduce((acc: Record<string, boolean>, guide: any) => {
        acc[guide.GuideId] = guide.GuideStep?.[0]?.Hotspot?.ShowByDefault || false; // All popups visible initially
        return acc;
      }, {});
      setPopupVisibility(initialVisibility);
    }
  }, [guideDetails]);
  const [hasViewed, setHasViewed] = useState(false);
  useEffect(() => {
    if ( !hasViewed && guides && guides.data.length > 0) {
      guides.data.forEach((guide: any) => {
        let timeDiff = Date.now() - initialTime;
        timeDiff = timeDiff/1000;
        if(guide?.GuideStep)
          {
  //           let tourClosed = localStorage.getItem('closedGuides_/');
  //     let tooltipExists = true;
  // if(tourClosed!=null){
  //   const guideArray: Guide[] = JSON.parse(tourClosed);
  //    tooltipExists = guideArray.some((x) => x.GuideType === "Tooltip");
  // }
          trackUserEngagement("guide-view", userData, guide, browser,version,"hotspotview",guide?.GuideStep?.[0]?.StepTitle,timeDiff,0);  // Pass the appropriate data
          }
        setHasViewed(true);
      });
    }
  });

  useEffect(() => {
    if (guideDetails && guideDetails.data && guideDetails.data.length > 0) {
      // Only include hotspots that are within their publish window
      const now = new Date();
      guideDetails.data = guideDetails.data.filter((x: any) => {
        if (x.GuideType.toLowerCase() !== "hotspot") return false;
        const publishDate = x.PublishDate ? new Date(x.PublishDate) : null;
        const unpublishDate = x.UnPublishDate ? new Date(x.UnPublishDate) : null;
        if (publishDate && now < publishDate) return false;
        if (unpublishDate && now > unpublishDate) return false;
        return true;
      });
      const storedGuides: Guide[] = JSON.parse(localStorage.getItem('closedGuides_/') || '[]');
      if (guideDetails && guideDetails.data && guideDetails.data.length > 0)
      {
        isGuideClosed = storedGuides.some((guide) => guide.GuideId === guideDetails?.data[0]?.GuideId && guide.DontShowAgain === true && guideDetails?.data[0].GuideType.toLowerCase() === "hotspot");
        if (isGuideClosed) {
          setAnchorEl(null);
        }
        else {
          setGuides((prev) => ({
            ...prev,
            data: guideDetails.data,
            loading: guideDetails.loading,
            error: guideDetails.error,
          }));

          setAnchorEl(document.body);
        }
      }
    }
  }, [guideDetails, currentUrl]);

  useEffect(() => {
    const handleStorageChange = () => {
      const storedGuides: Guide[] = JSON.parse(localStorage.getItem('closedGuides_/') || '[]');
      if (guides && guides.data && guides.data.length > 0)
      {
        isGuideClosed = storedGuides.some((guide) => guide.GuideId === guides?.data[0].GuideId && guide.DontShowAgain === true && guide?.GuideType.toLowerCase() === "hotspot");
        if (guides && guides.data[0]?.GuideStep?.length > 0 && isGuideClosed) {
          setAnchorEl(null);
        } else if (guides.data[0]?.GuideStep?.length > 0) {
          setAnchorEl(document.body);
        }
      }
    };
    handleStorageChange();
    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [guides, guides.data[0]?.GuideId, guides.data[0]?.GuideStep?.length]);

  const handleClose = (guideId: string) => {
    let timeDiff = Date.now() - initialTime;
    timeDiff = timeDiff/1000;
    trackUserEngagement("button-click", userData, guides.data[0], browser,version,"close",guides.data[0]?.GuideStep[0]?.StepTitle,timeDiff,0);

    setPopupVisibility((prev) => ({
      ...prev,
      [guideId]: false, // Close the popup for the specific guide
    }));
    const hotspotElement = document.getElementById(`hotspotBlink${guideId}`);
    if (hotspotElement) {
    hotspotElement.remove();
    }
    const storedGuides: Guide[] = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");

    var isHotSpotCloseSaved = storedGuides.some(
      (guide) => guide.GuideId ===guideId && guide.DontShowAgain === true && guide?.GuideType.toLowerCase() === 'hotspot'
      );
      if (!isHotSpotCloseSaved)
      {
          const updatedGuides = [
            ...storedGuides,
            {
              GuideId: guideId,
              DontShowAgain: true,
              GuideType: guides.data[0]?.GuideType
            },
        ];
          localStorage.setItem("closedGuides_/" , JSON.stringify(updatedGuides));
      }
    isGuideClosed = true;
    setAnchorEl(null)
  };


  const handleDontShowAgain = () => {
    setAnchorEl(null);
  };

  const handlePopupVisible = (data:any) => {
    setPopupVisible(data);
  }
  const getHotspotSteps = (guide: any) => {
    const step = guide.GuideStep[0];
    return {
      xpath: step?.ElementPath,
      hotspotProperties: {
        size: step?.Hotspot?.Size || "10",
        color: step?.Hotspot?.Color || "#f24545",
        type: step?.Hotspot?.Type || "circle",
        showUpon: step?.Hotspot?.ShowUpon || "Hovering Hotspot",
        showByDefault: step?.Hotspot?.ShowByDefault || false,
        stopAnimation: step?.Hotspot?.StopAnimation || false,
        pulseAnimation: step?.Hotspot?.PulseAnimation || false,
        position: step?.Hotspot?.HotspotPosition || { XOffset: "30", YOffset: "-5" },
      },
      targetUrl: step?.StepTargetURL || guide?.TargetUrl || "", // Use individual step URL if available, fallback to guide URL
      guideId: guide?.GuideId,
    }


  };

  return (
    <div>
      {guides.data && !isGuideClosed && (
        guides.data.map((guide: any) => (
          <>
            <Hotspot
              guideStep={getHotspotSteps(guide)}
              onPopupVisible={handlePopupVisible}
              setCurrentStep={setCurrentStep}
              key={guide.GuideId}
              guide={guide}
              guides={guides}
              currentStep={currentStep}
              handleClose={() => handleClose(guide.GuideId)}
              handleDontShowAgain={handleDontShowAgain}
              popupVisibility={popupVisibility}
              setPopupVisibility={setPopupVisibility}
              currentUrl={currentUrl}
              guideDetails={guideDetails}
              onContinue={() => { }}

            />



          </>
        ))
      )}

      </div>
  );
};

export default Hotspotview;
