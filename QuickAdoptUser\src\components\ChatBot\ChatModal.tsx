import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  IconButton,
  TextField,
  Paper,
  InputAdornment,
  Slide,
  Tooltip,
  CircularProgress,
  Divider,
  Button,
  LinearProgress
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import MicIcon from '@mui/icons-material/Mic';
import MicOffIcon from '@mui/icons-material/MicOff';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import VolumeOffIcon from '@mui/icons-material/VolumeOff';
import {
  isSpeechRecognitionSupported,
  startSpeechRecognition,
  stopSpeechRecognition
} from '../../services/SpeechRecognitionService';
import {
  speak,
  stop as stopSpeaking,
  setCurrentMessageId,
  isSpeechSynthesisSupported
} from '../../services/TextToSpeechService';
import {
  ChatModalStyle,
  Cha<PERSON><PERSON><PERSON><PERSON><PERSON>tyle,
  ChatBodyStyle,
  ChatFooterStyle,
  MessageContainerStyle,
  UserMessageStyle,
  BotMessageStyle,
  MicButtonStyle,
  ListeningIndicatorStyle,
  SpeechButtonStyle
} from './ChatBot.style';
import { SendMessageSSE, AssistantResponse as ServiceAssistantResponse,FetchWelcomeAudio,streamWelcomeAudio,speakWithOpenAITTS, fetchTTSStream } from '../Services/AssistantService';
import AIResponseDisplay from './AIResponseDisplay';
import { aichaticon, usrchaticon } from '../../assets/icons/icons';
import robot from "../../assets/icons/robot.png";
import { Preview } from '@mui/icons-material';
import { userApiService,speakModal } from '../Services/APIservice';

// Using ServiceAssistantResponse instead of this interface
// interface AssistantResponse {
//   ResponseType: string;
//   Message: string;
// }

interface Message {
  text: string;
  isUser: boolean;
  timestamp: Date;
  id?: string; // Unique identifier for each message
  guide: any;
  isStatusMessage?: boolean;
 // Flag for status messages
}
let base64Audio:any="";

interface ChatModalProps {
  open: boolean;
  onClose: () => void;
  guide: any;
  isFromAi: boolean;
  setGuide: (guide: any) => void;
  setIsFromAi: (isFromAi: boolean) => void;
  setIsOpen: (open: any) => void;
  newThreadCreated?: boolean;
  setNewThreadCreated?: React.Dispatch<React.SetStateAction<boolean>>;
  isWelcomeMessageShown: boolean;
  setWelcomeMessageShown: (isWelcomeMessageShown: any) => void;
  messages: any;
  setMessages: (messages: any) => void;
  isReWelcomeMessageShown: boolean;
  setReWelcomeMessageShown: (isReWelcomeMessageShown: any) => void;
}

const ChatModal: React.FC<ChatModalProps> = ({
  open,
  onClose,
  guide,
  isFromAi,
  setGuide,
  setIsFromAi,
  setIsOpen,
  newThreadCreated,
  setNewThreadCreated,
  isWelcomeMessageShown,
  setWelcomeMessageShown,
  messages,
  setMessages,
  isReWelcomeMessageShown,
  setReWelcomeMessageShown
}) => {

  // Initialize speakingModal variable and add debug logs
  var speakingModal = speakModal || "ElevenLabs";


  const [inputValue, setInputValue] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [speechRecognitionSupported, setSpeechRecognitionSupported] = useState(false);
  const [speakingMessageId, setSpeakingMessageId] = useState<string | null>(null);
  const [micPermissionError, setMicPermissionError] = useState<string | null>(null);
  const [micPermissionStatus, setMicPermissionStatus] = useState<string>('unknown');
  const [autoSpeakResponse, setAutoSpeakResponse] = useState<boolean>(true);
  const [lastResponseId, setLastResponseId] = useState<string | null>('welcome-message');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [firstName, setFirstName] = useState("");
  //const audioRef = useRef<HTMLAudioElement | null>(null);
  const currentAudioRef = useRef<HTMLAudioElement | null>(null);
  const audioQueueRef = useRef<string[]>([]);
  const abortControllerRef = useRef<AbortController | null>(null);
  const ttsCacheRef = useRef<Record<string, string[]>>({});

  

  
  const getUserData = () => {
    try {
      const userStatsString = localStorage.getItem('userStats');
      if (userStatsString) {
        return JSON.parse(userStatsString);
      }
      return null;
    } catch (error) {
      return null;
    }
  };

  // const fetchWelcomeAudio = async (text: string) => {
  //   base64Audio = await FetchWelcomeAudio(text);

  //   if (base64Audio) {
  //     const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
  //     audioRef.current = audio;
  //     audio.onplay = () => {
  //       // setSpeakingMessageId('audio-response');
  //       // setLastResponseId('audio-response');
  //     };
  //     audio.onpause = () => {
  //       stopAudio();
  //     };
  //     // Reset speaking state when audio finishes
  //     audio.onended = () => {
  //       setSpeakingMessageId(null);
      
  //       //startSpeech();
  //     };
  //     audio.play();
  //   }
  // };
  const fetchWelcomeAudio = async (text: string) => {
    if(speakingModal === "ElevenLabs")
    {
    const response = await streamWelcomeAudio(text); // Make API call to stream
  
    if (response) {
      const mime = 'audio/mpeg';
      const mediaSource = new MediaSource();
      const audio = new Audio();
      audioRef.current = audio;
      audio.src = URL.createObjectURL(mediaSource);
  
      audio.onplay = () => {
        // Optional: Handle onplay logic
      };
  
      audio.onpause = () => {
        stopAudio();
      };
  
      audio.onended = () => {
        setSpeakingMessageId(null);
      };
  
      mediaSource.addEventListener('sourceopen', () => {
        const sourceBuffer = mediaSource.addSourceBuffer(mime);
        const reader = response.getReader();
  
        const read = async () => {
          const { done, value } = await reader.read();
          if (done) {
            mediaSource.endOfStream();
            return;
          }
          sourceBuffer.appendBuffer(value);
          sourceBuffer.addEventListener('updateend', read, { once: true });
        };
  
        read();
      });
  
      audio.play();
    }
  }
  else{
       base64Audio = await FetchWelcomeAudio(text);

    if (base64Audio) {
      const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
      audioRef.current = audio;
      audio.onplay = () => {
        // setSpeakingMessageId('audio-response');
        // setLastResponseId('audio-response');
      };
      audio.onpause = () => {
        stopAudio();
      };
      // Reset speaking state when audio finishes
      audio.onended = () => {
        setSpeakingMessageId(null);
      
        //startSpeech();
      };
      audio.play();
    }
  }
  };

  // const fetchWelcomeAudio = async (text: string) => {
  //   const base64Chunks = await streamWelcomeAudioChunks(text);
  
  //   if (!base64Chunks || base64Chunks.length === 0) return;
  
  //   const playChunksSequentially = async (chunks: string[]) => {
  //     for (const base64Audio of chunks) {
  //       if (!base64Audio) continue;
  //       const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
  //       await new Promise((resolve) => {
  //         audio.onended = resolve;
  //         audio.onerror = resolve; // continue even if playback fails
  //         audio.play();
  //       });
  //     }
  //     setSpeakingMessageId(null);
  //   };
  
  //   const playAudioChunk = (base64: string): Promise<void> => {
  //     return new Promise((resolve) => {
  //       const audio = new Audio(`data:audio/mpeg;base64,${base64}`);
  //       audioRef.current = audio;
  //       audio.onended = () => resolve();
  //       audio.play(); 
  //     });
  //   };
  
  //   setSpeakingMessageId("welcome-message");
  //   playChunksSequentially(base64Chunks);
  // };
  //working one
  // const playTTSChunks = async (text: string) => {
  //   const res = await fetch("http://localhost:60552/api/Assistant/GetTTSChunks", {
  //     method: "POST",
  //     headers: { "Content-Type": "application/json" },
  //     body: JSON.stringify({ text })
  //   });
  
  //   const chunks = await res.json(); // [{ index, base64 }]
  //   const queue = chunks.map((c: any) => new Blob([Uint8Array.from(atob(c.base64), c => c.charCodeAt(0))], { type: "audio/mpeg" }));
  
  //   const playNext = () => {
  //     if (queue.length === 0) return;
  //     const blob = queue.shift();
  //     const audio = new Audio(URL.createObjectURL(blob));
  //     audio.onended = playNext;
  //     audio.play();
  //   };
  
  //   playNext();
  // };
  const playTTSChunks = async (messageId: string, text: string) => {
    if (speakingMessageId === messageId) {
      stopStreamedAudio();
      setSpeakingMessageId(null);
      return;
    }
  
    stopStreamedAudio(); // stop any existing
    setSpeakingMessageId(messageId);
  
    const queue: string[] = [];
    let isPlaying = false;
    let allChunksReceived = false;
  
    const playNext = () => {
      if (queue.length === 0) {
        if (allChunksReceived) {
          setSpeakingMessageId(null);
          // ✅ Now safe to start mic
          if (!isListening && !isProcessing && open) {
            startSpeech();
          }
        }
        return;
      }
  
      const src = queue.shift()!;
      const audio = new Audio(src);
      currentAudioRef.current = audio;
  
      audio.onended = () => {
        URL.revokeObjectURL(src);
        playNext();
      };
  
      audio.onerror = (e) => {
        console.error("Audio playback error", e);
        URL.revokeObjectURL(src);
        playNext();
      };
  
      audio.play().catch((err) => {
        console.error("Playback failed:", err);
        URL.revokeObjectURL(src);
        playNext();
      });
    };
  
    const controller = new AbortController();
    abortControllerRef.current = controller;
  
    await streamTTSChunks(text, controller.signal, (chunk) => {
      if (!chunk.base64) return;
  
      const byteArray = Uint8Array.from(atob(chunk.base64), (c) => c.charCodeAt(0));
      const blob = new Blob([byteArray], { type: "audio/mpeg" });
      const url = URL.createObjectURL(blob);
  
      queue.push(url);
      audioQueueRef.current.push(url);
  
      if (!isPlaying) {
        isPlaying = true;
        playNext();
      }
    });
  
    // ✅ This runs after all chunks are received
    allChunksReceived = true;
  
    // If playback already finished while chunks were being fetched
    if (queue.length === 0 && !isPlaying) {
      setSpeakingMessageId(null);
      if (!isListening && !isProcessing && open) {
        startSpeech();
      }
    }
  };
  
  const stopStreamedAudio = () => {
    if (currentAudioRef.current) {
      currentAudioRef.current.pause();
      currentAudioRef.current = null;
    }

    audioQueueRef.current.forEach((url) => {
      try {
        URL.revokeObjectURL(url);
      } catch (err) {
        console.warn("Failed to revoke URL:", err);
      }
    });
    audioQueueRef.current = [];

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    //setSpeakingMessageId(null);
    // if (!isListening && !isProcessing && open) {
    //   startSpeech();
    // }
  };
  

  const streamTTSChunks = async (
    text: string,
    signal: AbortSignal,
    onChunk: (chunk: { index: number; base64: string }) => void
  ) => {
    const stream = await fetchTTSStream(text, signal);
    const reader = stream.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = "";
  
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
  
      buffer += decoder.decode(value, { stream: true });
  
      let boundary = buffer.indexOf("\n\n");
      while (boundary !== -1) {
        const chunkStr = buffer.slice(0, boundary).trim();
        buffer = buffer.slice(boundary + 2);
  
        const dataLine = chunkStr.split("\n").find(line => line.startsWith("data: "));
        if (dataLine) {
          try {
            const jsonStr = dataLine.slice("data: ".length);
            const chunk = JSON.parse(jsonStr);
            onChunk(chunk);
          } catch (err) {
            console.error("JSON parse error:", err);
          }
        }
  
        boundary = buffer.indexOf("\n\n");
      }
    }
  };
  
  var welcomeText = "";

  var reWelcomeMessageText = "";

  const createWelcomeMessage = () => {
    const userData = getUserData();
  
    if (userData?.Name) {
      setFirstName(userData.Name);
    }
    setWelcomeMessageShown(true);

    const dynamicGreeting = firstName
      ? `Hi ${firstName}! I'm Dona, your AI-powered guide for everything in QuickAdopt.`
      : `Hi there! I'm Dona, your AI-powered guide for everything in QuickAdopt.`;
  
    welcomeText = `${dynamicGreeting} I can help you:
  
  • Build onboarding tours to walk new users through your features  
  • Design checklists to track key tasks step by step  
  • Launch surveys to collect feedback and improve your app  
  …and much more.
  
  You’ll also have support from Rookie, our interactive-guide expert. Whenever you’d like a hands-on, step-by-step walkthrough of any feature, just let us know: I’ll explain the “what” and “why,” and Rookie will generate a clickable tutorial so you can follow along in real time.`;
    
    setSpeakingMessageId("welcome-message");
    if(speakingModal === "webkit"){
      handleSpeakMessage("welcome-message",welcomeText);
    } else if(speakingModal ==="OpenAi"){
      fetchWelcomeAudio(welcomeText);
    }
    else{
      playTTSChunks("welcome-message",welcomeText);
    }
    
    return {
      text: welcomeText,
      isUser: false,
      timestamp: new Date(),
      id: 'welcome-message',
      guide: null
    };
  }

  const [hasInitialized, setHasInitialized] = useState(false);
  
  const [threadId, setThreadId] = useState<string | null>(localStorage.getItem("ThreadId"));

  useEffect(() => {
    if (open && !isWelcomeMessageShown && welcomeText == "" && messages.length === 0) {
      const welcomeMessageText = createWelcomeMessage();
      setWelcomeMessageShown(true);
      setMessages((prev : any) => [...prev, welcomeMessageText]);
    } else if (open && !isReWelcomeMessageShown && messages.length != 0 && reWelcomeMessageText === "") {
      const reWelcomeMessageText = createReWelcomeMessage();
      setReWelcomeMessageShown(true);
      setMessages((prev:any) => [...prev, reWelcomeMessageText]);
    }
  },[open])


  const createReWelcomeMessage = () => {
  
    const dynamicGreeting = firstName
      ? `Welcome back, ${firstName}!`
      : `Welcome back, `;
  
      reWelcomeMessageText = `${dynamicGreeting}

    Nice to have you here again. If you ever need help navigating the app or understanding a feature, just let me know—I'm here to guide you. `;
    setLastResponseId(`rewelcome-message_${messages.length - 1}`);
      setSpeakingMessageId(`rewelcome-message_${messages.length - 1}`);
      
      if(speakingModal === "webkit"){

        handleSpeakMessage(`rewelcome-message_${messages.length - 1}`,reWelcomeMessageText);

      } else if(speakingModal ==="OpenAi"){
        fetchWelcomeAudio(reWelcomeMessageText);
      }
      else{
        playTTSChunks(`rewelcome-message_${messages.length - 1}`,reWelcomeMessageText);
      }
      
    return {
      text: reWelcomeMessageText,
      isUser: false,
      timestamp: new Date(),
      id: `rewelcome-message_${messages.length - 1}`,
      guide: null
    };
  };


  
  //const [guide,setGuide] = useState();

  // Function to request microphone permission
  const requestMicrophonePermission = async (): Promise<boolean> => {
    try {
      // Request microphone permission explicitly
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // If we get here, permission was granted
      // Stop all tracks to release the microphone
      stream.getTracks().forEach(track => track.stop());

      return true;
    } catch (error) {
      console.error('Error requesting microphone permission:', error);
      return false;
    }
  };

  // Function to check microphone permission status
  const checkMicrophonePermission = async (): Promise<string> => {
    try {
      // Check if the permissions API is supported
      if (navigator.permissions && navigator.permissions.query) {
        const permissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName });

        return permissionStatus.state; // 'granted', 'denied', or 'prompt'
      } else {
        // Fallback for browsers that don't support the permissions API
        return 'unknown';
      }
    } catch (error) {
      console.error('Error checking microphone permission:', error);
      return 'unknown';
    }
  };

  // Function to explicitly request microphone permission
  const handleRequestMicrophonePermission = async () => {
    //console.log('Requesting microphone permission...');
    const permissionGranted = await requestMicrophonePermission();

    if (permissionGranted) {
      //console.log('Microphone permission granted');
      setMicPermissionStatus('granted');
      setMicPermissionError(null);
    } else {
      //console.log('Microphone permission denied');
      setMicPermissionStatus('denied');
      setMicPermissionError('Microphone permission denied. Please allow microphone access in your browser settings.');
    }
  };

  // Check if speech recognition and speech synthesis are supported
  useEffect(() => {
    // Check WebKit speech recognition support
    const recognitionSupported = isSpeechRecognitionSupported();
    setSpeechRecognitionSupported(recognitionSupported);
    if (!recognitionSupported) {
      console.warn('WebKit speech recognition is not supported in this browser');
    }

    // Check if speech synthesis is supported
    const synthesisSupported = isSpeechSynthesisSupported();
    if (!synthesisSupported) {
      console.warn('Speech synthesis is not supported in this browser');
      setAutoSpeakResponse(false); // Disable auto-speak if not supported
    }

    // Check microphone permission status
    const checkPermission = async () => {
      const permissionStatus = await checkMicrophonePermission();
      //console.log('Microphone permission status:', permissionStatus);
      setMicPermissionStatus(permissionStatus);

      // If permission is denied, set the error message
      if (permissionStatus === 'denied') {
        setMicPermissionError('Microphone permission denied. Please allow microphone access in your browser settings.');
      }
    };

    checkPermission();
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Generate a unique ID for messages
  const generateMessageId = (): string => {
    return `msg-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  };

  // Handle speaking a message
  const handleSpeakMessage = (messageId: string, text: string) => {
    if (speakingMessageId === messageId) {
      if(speakModal === "webkit"){
        stopSpeaking();
        if (!isListening && !isProcessing) {
          setTimeout(() => startSpeech(), 300);
        }
      } else if(speakModal ==="OpenAi"){
        stopAudio();

      }
      else{
        stopStreamedAudio();
        setSpeakingMessageId(null);
    if (!isListening && !isProcessing && open) {
      startSpeech();
    }
      }
      setSpeakingMessageId(null);
      return;
    }
    
    // If speaking another message, stop it first
    if (speakingMessageId) {
      //console.log('ChatModal: Stopping previous speech');
      if(speakModal === "webkit"){
        stopSpeaking();
      } else if(speakModal ==="OpenAi"){
        stopAudio();
      }
      else{
        stopStreamedAudio();
        setSpeakingMessageId(null);
    if (!isListening && !isProcessing && open) {
      startSpeech();
    }
      }
    }

    // Start speaking the new message
    //console.log('ChatModal: Starting to speak new message');
    setSpeakingMessageId(messageId);

    // Set the current message ID in the service
    setCurrentMessageId(messageId);
    if(speakModal === "webkit"){
      speak(text, {
        rate: 1.0, // Normal speed
        pitch: 1.0, // Normal pitch
        volume: 1.0, // Full volume
        onStart: () => {
          //console.log('ChatModal: Speech started for message:', messageId);
        },
        onEnd: () => {
          //console.log('ChatModal: Speech ended for message:', messageId);
          setSpeakingMessageId(null);
          // Only start speech recognition if not already listening and not processing
          if (!isListening && !isProcessing) {
            startSpeech();
          }
        },
        onError: (error) => {
          console.error('ChatModal: Speech synthesis error:', error);
          setSpeakingMessageId(null);
          // Only start speech recognition if not already listening and not processing
          if (!isListening && !isProcessing) {
           // startSpeech();
          }
        }
      });
     
    }else if(speakModal ==="OpenAi"){
      
    
    const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
    audioRef.current = audio;
    audio.onplay = () => {
      // setSpeakingMessageId('audio-response');
      // setLastResponseId('audio-response');
    };
    audio.onpause = () => {
      stopAudio();
    };
    // Reset speaking state when audio finishes
    audio.onended = () => {
      setSpeakingMessageId(null);
      
      //startSpeech();
      
    };
    audio.play();
  }
  else{
    playTTSChunks(messageId,text);
  }
  };
  
  
  const handleSendMessage = async (text?: string) => {
    // if (inputValue.trim() === '') return;

    // Add user message
    const userMessage: any = {
      text: text?.trim() ?? inputValue,
      isUser: true,
      timestamp: new Date(),
      id: generateMessageId(),
      guide: null
    };

    setMessages((prev:any) => [...prev!, userMessage]);
    
    const messageToSend = text?.trim() ?? inputValue;
    
    setInputValue('');

    // Set processing state to true
    setIsProcessing(true);

    // Variables to track the final message for text-to-speech
    let finalMessageId: string | null = null;
    
    let finalMessageText = '';

    try {
      await SendMessageSSE(messageToSend, {
        
        onMessage: (response: ServiceAssistantResponse) => {
          
          const botMessageId = generateMessageId();

          if(speakModal==="webkit" || (speakModal !=="webkit" && !response.Message.startsWith("[AUDIO]"))){
            finalMessageId = botMessageId;
          }
    
          if (!localStorage.getItem("chatHistoryId") && response.ChatHistoryId) {
            
            localStorage.setItem("chatHistoryId", response.ChatHistoryId);
          
          }
    
          // Check the response type and create the appropriate message
          let botMessage: Message;
    
          if (response.ResponseType === "guide") {
            
            if (response.Message != null && response.Message !== "") {
            
              response.Message = response.Message;
            
            } else {
            
              response.Message = "Here's the Guide, Click on Button to view the guide";
            
            }
    
            finalMessageText = response.Message;
    
            botMessage = {
              text: response.Message,
              isUser: false,
              timestamp: new Date(),
              id: botMessageId,
              guide: response.Guide,
              isStatusMessage: false
            };
            setMessages((prev:any) => [...prev, botMessage]);
          }
          else if (speakModal==="webkit" || (speakModal !=="webkit" && !response.Message.startsWith("[AUDIO]"))) {
            // Default to "message" type or any other type
            finalMessageText = response.Message;
            botMessage = {
              text: response.Message,
              isUser: false,
              timestamp: new Date(),
              id: botMessageId,
              guide: null,
              isStatusMessage:
                response.Message.startsWith("🔍") ||
                response.Message.startsWith("💬") ||
                response.Message.startsWith("🤖") ||
                response.Message.startsWith("📥") ||
                response.Message.startsWith("🧠")
            };
            
            setMessages((prev:any) => [...prev, botMessage]);
            
          }
          if (speakModal !=="webkit" && response.Message.startsWith("[AUDIO]")) {
            base64Audio = response.Message.replace("[AUDIO]", "");
            const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
            audioRef.current = audio;

            // Set speaking state when audio starts playing
            audio.onplay = () => {
            };
            audio.onpause = () => {
              stopAudio();
            };
          
            // Reset speaking state when audio finishes
            audio.onended = () => {
              setSpeakingMessageId(null);
              if (!isListening && !isProcessing) {
                //startSpeech();
              }
            };
          
            // Handle errors
            audio.onerror = () => {
              console.error("Error playing audio response");
              setSpeakingMessageId(null);
            };
          
            audio.play();
          }
    
        },
    
        onError: (error) => {
          console.error('ChatModal: Error from assistant service:', error);
    
          const errorMsg: Message = {
            text: `Error: Failed to get response from assistant. ${error instanceof Error ? error.message : 'Please try again.'}`,
            isUser: false,
            timestamp: new Date(),
            id: generateMessageId(),
            guide: null,
            isStatusMessage: true
          };
    
          //setMessages((prev:any) => [...prev, errorMsg]);
          
          setIsProcessing(false);
    
          if (!isListening) {
            startSpeech();
          }
        },
    
        onComplete: () => {
          setIsProcessing(false);
          
          setInputValue('');
    
          if (finalMessageId) {
            
            const messageId = finalMessageId;
            
            const messageText = finalMessageText;
    
            setLastResponseId(messageId);
            
            setSpeakingMessageId(messageId);
            
            if (autoSpeakResponse) {
              setTimeout(() => {
                if (speakModal === "webkit") {
                  handleSpeakMessage(messageId, messageText);
                } else if(speakModal === "ElevenLabs") {
                  playTTSChunks(messageId,messageText);
                }
                // else{
                //   speakWithOpenAITTS(messageText);
                // }
              }, 300);
            }
            
            // if (autoSpeakResponse) {
            //   setTimeout(() => {
            //     speakWithOpenAITTS(messageText);
            //   }, 300);
            // }
          }
        }
      });
    } catch (error) {
      console.error('Error sending message:', error);

      // Add error message to chat
      const errorMsg: Message = {
        text: `Error: Failed to connect to the assistant. ${error instanceof Error ? error.message : 'Please try again.'}`,
        isUser: false,
        timestamp: new Date(),
        id: generateMessageId(),
        guide: null,
        isStatusMessage: true
      };

      setMessages((prev:any) => [...prev, errorMsg]);
      setIsProcessing(false);
    }
  };
  
  const stopAudio = (startListening: boolean = true) => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0; // Reset to beginning
      audioRef.current = null;
      setSpeakingMessageId(null);
      if (!isListening && !isProcessing && startListening && open) {
        startSpeech();
      }
    }
  };
  
  

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isProcessing && !isListening && inputValue.trim() !== '') {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Toggle WebKit speech recognition
  const startSpeech = async () => {
    // Clear any previous error
    setMicPermissionError(null);

    // Don't start speech recognition if already processing a message
    if (isProcessing) {
      console.log('ChatModal: Cannot start speech recognition while processing a message');
      return;
    }

    if (isListening) {
      //console.log('ChatModal: Stopping WebKit speech recognition');
      stopSpeechRecognition();
      setIsListening(false);
      // Input value is already set with the latest transcript
    } else {
      //console.log('ChatModal: Starting WebKit speech recognition');

      // First, explicitly request microphone permission
      const permissionGranted = await requestMicrophonePermission();

      if (!permissionGranted) {
        //console.log('ChatModal: Microphone permission denied for WebKit');
        setMicPermissionStatus('denied');
        setMicPermissionError('Microphone permission denied. Please allow microphone access in your browser settings.');
        return;
      }

      // Update permission status
      setMicPermissionStatus('granted');
      setMicPermissionError(null);

      setIsListening(true);
      // Clear the input field when starting speech recognition
      setInputValue('');

      try {
        startSpeechRecognition({
          onStart: () => {
             setIsListening(true);
            setMicPermissionError(null);
          },
          onResult: (text, _isFinal) => {
            setInputValue(text);
          },
          onEnd: (text: string) => {
            setIsListening(false);
            // Only send the message if we're not already processing another message
            if (!isProcessing && text.trim() !== '') {
              handleSendMessage(text);
            } else if (isProcessing) {
              console.log('ChatModal: Cannot send message while processing another message');
            }
          },
          onError: (error) => {
            console.error('ChatModal: WebKit speech recognition error:', error);
            setIsListening(false);
          }

          // let errorMessage = 'Speech recognition error';
          // if (typeof error === 'string') {
          //   errorMessage = error;
          // }

          // Check for permission errors
          // if (errorMessage.toLowerCase().includes('permission') ||
          //     errorMessage.toLowerCase().includes('denied') ||
          //     errorMessage.toLowerCase().includes('access')) {
          //   errorMessage = 'Microphone permission denied. Please allow microphone access in your browser settings.';
          //   setMicPermissionStatus('denied');
          // }

          // setMicPermissionError(errorMessage);

          // Add error message to chat
          //   const errorMsg: Message = {
          //     text: `Microphone error: ${errorMessage}`,
          //     isUser: false,
          //     timestamp: new Date(),
          //     id: generateMessageId(),
          //     guide: null
          //   };
          //   setMessages(prev => [...prev, errorMsg]);
          // },

          // pauseDuration: 2000 // Stop after 2 seconds of silence
        });
      } catch (error) {
        console.error('ChatModal: Failed to start WebKit speech recognition:', error);
        //setIsListening(false);

        // Set user-friendly error message
        let errorMessage = 'Failed to start speech recognition';
        if (error instanceof Error) {
          errorMessage = error.message;
        }

        // Check for permission errors
        if (errorMessage.toLowerCase().includes('permission') ||
          errorMessage.toLowerCase().includes('denied') ||
          errorMessage.toLowerCase().includes('access')) {
          errorMessage = 'Microphone permission denied. Please allow microphone access in your browser settings.';
          setMicPermissionStatus('denied');
        }

        setMicPermissionError(errorMessage);

        // Add error message to chat
        const errorMsg: Message = {
          text: `Microphone error: ${errorMessage}`,
          isUser: false,
          timestamp: new Date(),
          id: generateMessageId(),
          guide: null
        };
        setMessages((prev:any) => [...prev, errorMsg]);
      }
    }
  };

  // Function to toggle auto-speak for responses
  const toggleAutoSpeak = () => {
    setAutoSpeakResponse(prev => !prev);
  };


  const handleCloseModal = useCallback(() => {


    // Stop listening and reset states FIRST
    setIsListening(false);
    setReWelcomeMessageShown(false);
    stopSpeechRecognition();

    // Stop speaking based on the speaking modal type
    if (speakingModal === "OpenAi") {

      stopAudio(false);
    } else if (speakingModal === "ElevenLabs") {

      stopStreamedAudio();
    } else {

      stopStreamedAudio();
    }

    // Clean up speaking state
    if (speakingMessageId) {
     
      stopSpeaking();
      setSpeakingMessageId(null);
      setSpeechRecognitionSupported(false);
    }

    // Close the modal LAST
    onClose();
   
  }, [speakingModal, speakingMessageId, onClose, stopSpeechRecognition, stopAudio, stopStreamedAudio, stopSpeaking, setSpeakingMessageId, setSpeechRecognitionSupported, setIsListening, setReWelcomeMessageShown]);


  return (<>{
    open &&
    (
      <Paper sx={{
        position: "relative",
        width: "380px",
        display: "flex",
        flexDirection: "column",
        borderRadius: "0px",
        boxShadow: "0px 0px 15px rgba(0,0,0,0.2)",
        overflow: "hidden",
        zIndex: "1",
        height: "100vh",
        background: '#F8F9FA',
        // padding: '10px'
      }} elevation={3}>
        {/* Chat Header */}
        <Box sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "8px 12px",
          color: "white",
          position: "relative",
          overflow: "hidden",
          boxShadow: "0px 5px 10px rgba(0, 0, 0, 0.2)",
          // borderRadius: "10px",
          // backdropFilter: 'blur(1.5px)',
          // borderRadius: '356px',
          // background:' #C456EA',
          // filter: 'blur(45px)',
          "&::before": {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: '#4361EE',
            zIndex: -1
          }
        }}>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}>
            <img
              src={robot}
              alt="Chat"
              style={{
                width: "36px",
                height: "36px"
              }}
            />
            <Typography
              sx={{
                background: '#fff',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: "22px",
                fontWeight: "600",
                fontFamily: "Gotham Pro",
              }}>Dona</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {/* Auto-speak toggle button */}
            <Tooltip
              title={autoSpeakResponse ? "Turn off auto-speak" : "Turn on auto-speak"}
              componentsProps={{
                tooltip: {
                  sx: {
                    fontSize: "12px",
                    fontFamily: "Gotham Pro",
                    color: "white",
                  }
                }
              }}
            >
              <IconButton
                onClick={toggleAutoSpeak}
                size="small"
                sx={{
                  color: 'white', marginRight: 1,
                  svg: {
                    fontSize: "18px"
                  }
                }}
              >
                {autoSpeakResponse ? <VolumeUpIcon /> : <VolumeOffIcon />}
              </IconButton>
            </Tooltip>
            <IconButton
              size="small"
              onClick={handleCloseModal}
              sx={{
                color: 'white',
                svg: {
                  fontSize: "18px"
                }
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>

        </Box>

        {/* Chat Body - Messages */}
        <Box sx={{
          flexGrow: 1,
          overflowY: "auto",
          padding: "0px",
          display: "flex",
          flexDirection: "column",
          gap: "8px",
          height: "calc(90vh - 110px)",
          placeContent: "flex-end",
          //     '&::-webkit-scrollbar': {
          //   width: '6px',
          // },
          // '&::-webkit-scrollbar-track': {
          //   backgroundColor: '#f0f0f0',
          // },
          // '&::-webkit-scrollbar-thumb': {
          //   backgroundColor: '#a0a0a0',
          //   borderRadius: '4px',
          //       },
        }}>
          <Box sx={{
            display: "flex",
            flexDirection: "column",
            gap: "8px",
            width: "100%",
            height: "auto",
            overflow: "auto",
            padding: "16px",
          }}>
            {messages.map((message:any) => (
              <Box
                key={message.id || `msg-${message.timestamp.getTime()}`}
                sx={{
                  alignSelf: message.isUser ? "flex-end" : "flex-start",
                  // backgroundColor: message.isUser ? "#e3f2fd" : "#f5f5f5",
                  // padding: "8px 12px",
                  // borderRadius: message.isUser ? "18px 18px 0 18px" : "18px 18px 18px 0",
                  width: "100%",
                  maxWidth: "100%",
                  wordBreak: "break-word",
                }}
              >

                <Box sx={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  width: '100%'
                }}>
                  {
                    message.isUser
                      ?
                      <Box sx={{
                        width: '100%',
                        textAlign: "right"
                      }}>
                        <Typography sx={{
                          fontSize: "14px",
                          color: " #000",
                          fontFamily: "Gotham Pro",
                          borderWidth: "0px",
                          padding: "12px 14px",
                          background: "#DEE2E6",
                          width: "fit-content",
                          borderRadius: "20px",
                          float: "right",
                          maxWidth: "95%"
                        }}>
                          {message.text}
                        </Typography>
                      </Box>
                      :
                      <Box sx={{ flex: 1 }}>
                        {
                          message.guide == null && <AIResponseDisplay text={message.text} />
                        }
                        {

                          message.guide != null &&
                          <>
                            <Button variant="outlined" size="small" color="primary" onClick={() => {
                              setIsOpen(false); setIsFromAi(true); setGuide(message.guide);
                              stopSpeechRecognition();
                              setReWelcomeMessageShown(false);
                              setIsListening(false);
                              if(speakModal ==="OpenAi"){
                                stopAudio(false);
                                
                              }
                              else if(speakModal ==="ElevenLabs")
                              {
                             stopStreamedAudio();
                              }
                              if (speakingMessageId) {
                                stopSpeechRecognition();
                                stopSpeaking();
                                setSpeakingMessageId(null);
                              }
                            }}
                              sx={{
                                color: "#4361ee",
                                borderColor: "#4361ee",
                                marginTop: "8px",
                                fontSize: "10px",
                                fontWeight: "600",
                                "&:hover": {
                                  color: "#fff",
                                  borderColor: "#4361ee",
                                  background: "#4361ee",
                                },
                              }}
                            >{message.text}</Button>
                          </>

                        }
                      </Box>
                  }

                  {/* Text-to-speech button only for the last bot message */}
                  {!message.isUser && message.id && (
                    <Tooltip title={speakingMessageId === message.id ? "Stop speaking" : "Listen again"}
                      componentsProps={{
                        tooltip: {
                          sx: {
                            fontSize: "12px",
                            fontFamily: "Gotham Pro",
                            color: "white",
                          }
                        }
                      }}
                    >
                      <IconButton
                        disabled={isListening || message.id !== lastResponseId}
                        onClick={() => { if (isListening) return; handleSpeakMessage(message.id!, message.text) }}
                        size="small"
                        sx={{
                          marginRight: '10px',
                          color: '#000',
                          '&:hover': {
                            color: '#000',
                            background: "transparent",
                          },
                          // color: speakingMessageId === message.id ? "#5f9ea0" : "inherit"
                        }}
                      >
                        {speakingMessageId === message.id ? <VolumeUpIcon /> : <VolumeOffIcon />}
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              </Box>
            ))}
            <div ref={messagesEndRef} />
          </Box>
        </Box>

        {/* Processing indicator */}
        {/* {isProcessing && (
          <Box sx={{
            padding: '8px 16px',
            backgroundColor: 'rgba(95, 158, 160, 0.1)',
            borderTop: '1px solid rgba(95, 158, 160, 0.2)'
          }}>
            <LinearProgress
              sx={{
                height: 4,
                borderRadius: 2,
                backgroundColor: 'rgba(95, 158, 160, 0.2)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: '#5f9ea0'
                }
              }}
            />
          </Box>
        )} */}

        {/* Removed the separate interim transcript display */}

        {/* Microphone permission error message with request button */}
        {false && (
          <Box sx={{
            padding: '8px 16px',
            backgroundColor: '#ffebee',
            color: '#d32f2f',
            borderTop: '1px solid #ffcdd2',
            fontSize: '0.875rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <Typography variant="body2" color="error">
              {micPermissionError}
            </Typography>

            {/* Button to request microphone permission */}
            <Button
              variant="outlined"
              size="small"
              color="error"
              onClick={handleRequestMicrophonePermission}
              sx={{ ml: 2, whiteSpace: 'nowrap' }}
            >
              Request Permission
            </Button>
          </Box>
        )}

        {/* Chat Footer - Input */}
        <Box sx={{
          background: '#e7e7e7',
          // boxShadow: '0px -4px 10px rgba(0, 0, 0, 0.2)',
          display: "flex",
          alignItems: "center",
          gap: "8px",
          padding: "0px 14px",
        }}>
          <Box sx={{
            display: "flex",
            alignItems: "center",
            gap: "8px",
            margin: "10px 0px",
            paddingRight: "10px",
            width: "100%",
            borderRadius: "10px",
            background: "transparent",
            border: "1px solid #a3a3a3",
          }}>
            {/* Bot Icon (optional if you're using it like in the image) */}
            {/* <Box
    sx={{
      width: 32,
      height: 32,
      borderRadius: '50%',
      backgroundColor: '#000',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
  >
    <SmartToyIcon sx={{ color: '#fff', fontSize: 18 }} />
  </Box> */}

            {/* TextField */}
            <TextField
              fullWidth
              multiline
              placeholder={
                isListening
                  ? "I'm Listening..."
                  : isProcessing
                    ? "Processing your request..."
                    : "I'm here to help you..."
              }
              variant="outlined"
              size="small"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={isListening || isProcessing}
              inputRef={inputRef}
              InputProps={{
                // startAdornment: (
                //   <InputAdornment position="start">
                //     <Box
                //       sx={{ display: 'flex', alignItems: 'center',}}
                //       dangerouslySetInnerHTML={{ __html: aichaticon }}
                //     />
                //   </InputAdornment>
                // ),
                sx: {
                  padding: "6px 0px 6px 12px",
                  // borderRadius: '30px',
                  color: '#fff !important',
                  border: "0px !important",
                  // backgroundColor: isProcessing ? 'rgba(97, 42, 116, 0.1)' : 'transparent',
                  // transition: 'background-color 0.3s ease',
                  '& textarea': {
                    minHeight: '25px',
                    maxHeight: '100px',
                    overflowY: 'auto !important',
                    height: "50px",
                    // placeContent: "center",
                    fontSize: "13px",
                    color: "#000",
                    // '&::-webkit-scrollbar': {
                    //   width: '6px',
                    // },
                    // '&::-webkit-scrollbar-track': {
                    //   backgroundColor: '#f0f0f0',
                    // },
                    // '&::-webkit-scrollbar-thumb': {
                    //   backgroundColor: 'transparent',
                    // borderRadius: '4px',
                    // },
                    // '&.Mui-disabled': {
                    //   WebkitTextFillColor:"#fff !important",
                    // }
                    // },
                    // '& .MuiOutlinedInput-notchedOutline': {
                    //   border: isProcessing
                    //     ? '2px solid transparent'
                    //     : '1px solid #444746',
                    //   borderRadius: '30px',
                    // },
                    // '&:hover .MuiOutlinedInput-notchedOutline': {
                    //   border: isProcessing
                    //     ? '2px solid transparent'
                    //     : '1px solid #444746',
                    // },
                    // '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    //   border: isProcessing
                    //     ? '2px solid transparent'
                    //     : '1px solid #444746',
                  },
                  '& fieldset': {
                    border: '0px',
                  },
                },
              }}
            />
            {/* Mic Button */}
            {speechRecognitionSupported && (
              <Tooltip
                title={
                  isListening
                    ? "Stop listening"
                    : micPermissionStatus === 'denied'
                      ? "Microphone permission denied. Click to request access."
                      : "Start voice input"
                }
                componentsProps={{
                  tooltip: {
                    sx: {
                      fontSize: "12px",
                      fontFamily: "Gotham Pro",
                    }
                  }
                }}
              >
                <IconButton
                  disabled={!!speakingMessageId}
                  onClick={
                    micPermissionStatus === 'denied'
                      ? handleRequestMicrophonePermission
                      : startSpeech
                  }
                  size="small"
                  sx={{
                    backgroundColor: 'transparent', // Purple background
                    color: '#000',
                    '&:hover': {
                      backgroundColor: 'transparent',
                    },
                  }}
                >
                  {isListening ? (
                    <>
                      <MicIcon sx={{ fontSize: "20px" }} />
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <CircularProgress
                          size={24}
                          thickness={3}
                          sx={{ color: '#000' }}
                        />
                      </Box>
                    </>
                  ) : micPermissionStatus === 'denied' ? (
                    <MicOffIcon sx={{ fontSize: "20px" }} />
                  ) : (
                    <MicIcon sx={{ fontSize: "20px" }} />
                  )}
                </IconButton>
              </Tooltip>
            )}
          </Box>
          {/* Send Button */}
          <IconButton
              onClick={() => {
                handleSendMessage(); 
                if(speakModal !=="webkit"){
                stopAudio();
                }
              }}
            disabled={inputValue.trim() === '' || isListening || isProcessing}
            size="small"
            sx={{
              padding: "9px 8px 11px 12px",
              color: '#fff',
              backgroundColor: '#4361ee',
              '&.Mui-disabled': {
                backgroundColor: '#4361ee',
                opacity: 0.6,  // optional: add some visual cue it's disabled
                color: '#fff',
              },
              '&:hover': {
                backgroundColor: '#4361ee',
              },
            }}
          >
            <SendIcon sx={{
              fontSize: "20px",
              transform: "rotate(-35deg)",
            }} />
          </IconButton>
        </Box>

      </Paper>
    )
  }
  </>);
};

export default ChatModal;
