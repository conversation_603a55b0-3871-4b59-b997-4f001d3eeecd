import { Box, Button, Typography, DialogActions } from "@mui/material";
import { useEffect, useMemo, useState, version } from "react";
import { BannerWrapper, IconButtonSX, TextWrapper, InnerWrapper} from "./Banner.style";
import { CustomIconButton } from "../Button";
import CloseIcon from "@mui/icons-material/Close";
import { IReponse, useFetch } from "../../hooks/useFetch";
import { POST } from "../../service/APIService";
import { upload } from "@testing-library/user-event/dist/upload";
import { browser, trackUserEngagement, userData } from "../UserEngagement/userEngagementTracking";
import { useUrlChange } from "../../hooks/useUrlChange";
interface Guide {
    GuideId: string;
    DontShowAgain: boolean;
    GuideType: string;
}
let isGuideClosed: boolean = false;
const Banner = () => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(document.body);
    const [showBanner, setShowBanner] = useState(false);

    // Use our custom URL change detection hook
    const currentUrl = useUrlChange();

    let initialTime: any;
    const [response, setResponse] = useState<IReponse>({
		data: [],
		loading: false,
		error: {
			message: "",
			isError: false,
		},
    });
    let accountId = localStorage.getItem('AccountId') ?? "";
    const [guideDetails] = useFetch({
      url: `/EndUserGuide/GetGuideListByTargetUrl?targetUrl=${encodeURIComponent(currentUrl)}&accountId=${accountId}`,
      dependencies: [currentUrl], // Re-fetch when currentUrl changes
      timeout: 30000, // 30 second soft timeout (logs warning but allows slow APIs)
      hardTimeout: 120000 // 2 minute hard timeout for very slow APIs
    });

    // Reset banner state when URL changes - clear old banners immediately
    useEffect(() => {
        //console.log('🔄 Banner: URL changed, clearing old banners');
        setShowBanner(false);
    }, [currentUrl]);
    
    const Banner = useMemo(() => {
        return (response.data as any[]).find((item) => item.GuideType === "Banner") || {};
    }, [response]);
    const canvas = Banner?.GuideStep?.[0]?.Canvas || {};
    const htmlSnippet = Banner?.GuideStep?.[0]?.HtmlSnippet || "";
 //canvas properties
    const BackgroundColor = canvas.BackgroundColor || "#f1f1f7";
    const Width = canvas.Width || "100%"; // Default to 100%
    const Radius = canvas.Radius || "0"; // Default radius
    const Padding = canvas.Padding || "10"; // Default padding
    const BorderSize = canvas.BorderSize || "2"; // Default border size
    const BorderColor = canvas.BorderColor || "#f1f1f7"; // Default border color
    const isCoverTop =
        !canvas ||                            // canvas is undefined or null
        Object.keys(canvas).length === 0 ||   // canvas is empty
        !canvas.Position ||                   // position is missing or empty
        canvas.Position === "Cover Top";      // position matches expected
    const Position = "absolute";
    const zindex = canvas.Zindex || "999999";

    const renderHtmlSnippet = (snippet: string): string => {
        if (!snippet) return '';

        // This regex matches all <a> tags and ensures they have target="_blank" and rel="noopener noreferrer" attributes
        return snippet.replace(/(<a\s+[^>]*?)(href=["']([^"']*)["'])([^>]*?)(>)/g, (match, before, hrefPart, url, after, closing) => {
            // Check if target attribute already exists
            const hasTarget = /target=["'][^"']*["']/.test(before + after);
            const hasRel = /rel=["'][^"']*["']/.test(before + after);

            let result = before + hrefPart;

            // Add target attribute if it doesn't exist
            if (!hasTarget) {
                result += ' target="_blank"';
            }

            // Add rel attribute if it doesn't exist
            if (!hasRel) {
                result += ' rel="noopener noreferrer"';
            }

            result += after + closing;
            return result;
        });
    };

    // Dissmiss icon property
    const Modal = Banner?.GuideStep?.[0]?.Modal;
    const stepData = Banner?.GuideStep?.[0]?.StepTitle;
    const isCloseDisabled = Modal?.DismissOption ?? false;
    const uploadedImages = Banner?.GuideStep?.[0]?.ImageProperties[0]?.CustomImage;
    const image = uploadedImages && uploadedImages.length > 0 ? uploadedImages[0] : null;
    const isBase64 = (url: any) => {
        return url ? url.startsWith("data:image/") : false;
    };

    // text properties
    const textField = Banner?.GuideStep?.[0]?.TextFieldProperties[0] || {};
    const { Text: textFieldText, Alignment, Hyperlink, Emoji, TextProperties } = textField;
    const { Bold, Italic, BulletPoints, TextColor, TextFormat } = TextProperties || {};
    // Custom button properties
    const CustomButton = Banner.GuideStep?.[0]?.ButtonSection[0]?.CustomButtons || {};
    // Access ButtonProperties and get the Padding value
    const ButtonPadding = CustomButton.ButtonProperties?.Padding;
    const FontSize = CustomButton.ButtonProperties?.FontSize;
    const ButtonWidth = CustomButton.ButtonProperties?.Width;
    const Font = CustomButton.ButtonProperties?.Font;
    const ButtonTextColor = CustomButton.ButtonProperties?.ButtonTextColor;
    const ButtonBackgroundColor = CustomButton.ButtonProperties?.ButtonBackgroundColor;

    const ButtonAlignment = CustomButton.Alignment || "center";
    const ButtonAction = CustomButton.ButtonAction || {};
    const {
        Top: PaddingTop = 0,
        Left: PaddingLeft = 0,
        Right: PaddingRight = 0,
        Bottom: PaddingBottom = 0,
    } = CustomButton.Padding || {};

    // Design properties
    const designProps = Banner?.GuideStep?.[0]?.Design || {};
    const BannerWidth = designProps.ViewPortWidth || "100%";
    const BackdropShadow = "0px 1px 15px rgba(0, 0, 0, 0.7)";
    const IconColor = designProps.IconColor || "#000";
    // const IconOpacity = designProps.QuietIcon ? 0.5 : 1.0; // Reduce opacity if QuietIcon is true

    // advanced properties
    const showAfter = Banner?.GuideStep?.[0]?.Advanced?.ShowAfter || "0s";
    const showDelay = parseInt(showAfter) * 1000;
    initialTime = Date.now();
    // Handle restart action
    const handleRestart = () => {
        // Track user engagement for restart action
        let timeDiff = Date.now() - initialTime;
        timeDiff = timeDiff/1000;
        trackUserEngagement("button-click", userData, Banner, browser,version,"restart",stepData,timeDiff,0);

        // Implement restart functionality if needed
        // For Banner, there's typically only one step, so no need to reset step index
    };

    const handleButtonClick = (action: any,buttonName:any) => {
        let timeDiff = Date.now() - initialTime;
        timeDiff = timeDiff/1000;
        if (action.Action === "open-url" || action.Action === "open"|| action.Action === "openurl") {

            trackUserEngagement("button-click", userData, Banner, browser,version,buttonName,stepData,timeDiff,0);
            if (action.ActionValue === "same-tab") {
                window.location.href = action.TargetUrl;
            } else {
                window.open(action.TargetUrl, "_blank", "noopener,noreferrer");
            }
                        //onContinue();
            //console.log("targetur", action.TargetUrl);
        } else if (action.Action === "start-interaction") {
            // onContinue();
            // setOverlayValue(false);
        } else if (action.Action === "close" || action.Action ==="") {

            trackUserEngagement("button-click", userData, Banner, browser,version,buttonName,stepData,0,timeDiff);
            handleCloseBanner();
            // onClose();
            // setOverlayValue(false);
        } else if (action.Action && action.Action.toLowerCase() === "restart") {
            handleRestart();
        } else if (action === undefined || null) {
            // onClose();
            // setOverlayValue(false);
        } else {
            // onClose();
            // setOverlayValue(false);
        }
    };
    const handleCloseBanner = () => {
        let timeDiff = Date.now() - initialTime;
        timeDiff = timeDiff/1000;
        trackUserEngagement("button-click", userData, Banner, browser,version,"close",stepData,0,timeDiff);

        const storedGuides: Guide[] = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");
        var isbannerCloseSaved = storedGuides.some(
            (guide) => guide.GuideId === Banner.GuideId && guide.DontShowAgain === true && guide?.GuideType.toLowerCase() === 'banner'
        );
        if (!isbannerCloseSaved)
        {
            const updatedGuides = [
                ...storedGuides,
                {
                    GuideId: Banner.GuideId,
                    DontShowAgain: true,
                    GuideType: Banner.GuideType
                },
            ];
            isGuideClosed = true;
            localStorage.setItem("closedGuides_/", JSON.stringify(updatedGuides));

        }

        const body = document.querySelector("body") as HTMLElement;
        if (body) {

            body.style.setProperty("padding-top", "0px", "important");
            body.style.setProperty("max-height", "initial", "important");
        }
        setShowBanner(false);
    };

    const addDynamicTag = () => {
        let styleTag = document.getElementById("dynamic-banner") as HTMLStyleElement;
        document.body.classList.add("dynamic-banner");
        if (!styleTag) {
            styleTag = document.createElement("style");
            styleTag.id = "dynamic-banner";
            if (!isCoverTop) {
                const height = 31 + parseInt(Padding)*2 + parseInt(BorderSize)*2 + (Array.isArray(CustomButton) ? 9 : 0 );
                const styles = `
                .dynamic-banner {
                    padding-top: ${height}px !important;
                    max-height: calc(100% - 55px);
                }
                  .dynamic-body-style header {
						top: ${height}px !important;
					}
					
                        		.dynamic-body-style .page-sidebar {
						padding-top: ${height}px !important;
					}
            `;
                styleTag.innerHTML = styles;
                document.head.appendChild(styleTag);
            }

        }

    }

    useEffect(() => {
        if (response && Banner?.GuideStep) {
            const currentDate = new Date();
            const publishDate = Banner?.PublishDate ? new Date(Banner.PublishDate) : null;
            const unpublishDate = Banner?.UnPublishDate ? new Date(Banner.UnPublishDate) : null;

            // Only show the banner if current time is within the publish window
            if (
                publishDate &&
                currentDate >= publishDate &&
                (!unpublishDate || currentDate <= unpublishDate)
            ) {
                const timer = setTimeout(() => {
                    let timeDiff = Date.now() - initialTime;
                    timeDiff = timeDiff/1000;
                    trackUserEngagement("guide-view", userData, Banner, browser,version,"view",stepData,0,timeDiff);
                    const banner = document.getElementById("quickAdopt_banner") as HTMLElement | null;
                    if (banner && !isGuideClosed) {
                        addDynamicTag();
                    }
                    setShowBanner(true);
                }, showDelay);
                return () => clearTimeout(timer);
            } else {
                setShowBanner(false);
            }
        } else {
            setShowBanner(false);
        }
    }, [response, showDelay, Banner?.GuideStep]);

    useEffect(() => {
        const handleStorageChange = () => {
            const storedGuides: Guide[] = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");
            isGuideClosed = storedGuides.some(
                (guide) => guide.GuideId === Banner.GuideId && guide.DontShowAgain === true && guide?.GuideType?.toLowerCase() === 'banner'
            );
            const currentDate = new Date();
            const publishDate = Banner?.PublishDate ? new Date(Banner.PublishDate) : null;
            const unpublishDate = Banner?.UnPublishDate ? new Date(Banner.UnPublishDate) : null;
            if (response && Banner?.GuideStep?.length > 0 && isGuideClosed) {
                setShowBanner(false);
            } else if (
                Banner?.GuideStep?.length > 0 &&
                publishDate &&
                currentDate >= publishDate &&
                (!unpublishDate || currentDate <= unpublishDate)
            ) {
                setShowBanner(true);
                addDynamicTag();
            } else {
                setShowBanner(false);
            }
        };

        handleStorageChange();
        window.addEventListener("storage", handleStorageChange);
        return () => {
            window.removeEventListener("storage", handleStorageChange);
        };
    }, [response, Banner.GuideId, Banner?.GuideStep?.length, Banner?.PublishDate, Banner?.UnPublishDate]);


    useEffect(() => {
        if (guideDetails && guideDetails.data && guideDetails.data.length > 0) {
          setResponse(prev => ({
            ...prev,
            data: guideDetails.data,
            loading: guideDetails.loading,
            error: guideDetails.error,
          }));
        }
      }, [guideDetails]);
      useEffect(() => {
		if (canvas?.Position === "Cover Top") {
			document.body.style.overflow = "hidden";
		} else {
			document.body.style.overflow = "";
		}

		// Cleanup function to restore overflow when component unmounts
		return () => {
			document.body.style.overflow = "";
		};
	}, [canvas?.Position]); // Re-run when canvas position changes
    // const handleCloseBanner = async () => {
    //   const newData = { ...Banner, Visited: true, VisitedDate: new Date().toISOString() };
    //   try {s
    //     await POST("/EndUserGuide/Updateguide", newData);
    //     setShowBanner(false);
    //   } catch (error) {
    //     //console.log(error, "Post error");
    //   }
    // };

    // const handleCloseBanner = () => {
    //   localStorage.setItem('bannerClosed', 'true'); // Set closed state persistently
    //   // localStorage.setItem('bannerPushedDown', 'true');
    //   const body = document.querySelector("body") as HTMLElement;

    //   if (body) {
    //     body.style.marginTop = "0"; // Reset the body margin
    //   }
    //   else {
    //   }
    //   // const banner = document.getElementById("div");
    //   //       banner?.remove();
    //   // const body = document.querySelector('body') as HTMLElement;
    //   // body.style.marginTop = '0px'
    //   setShowBanner(false);
    // };



    // Define handler to bind action on button click


    return (
        <div className="qadpt-banendusr">
            {showBanner  && !isGuideClosed && (
                <Box
                    sx={{
                        ...BannerWrapper,
                        maxWidth: BannerWidth,
                        boxShadow: isCoverTop ? BackdropShadow : "none",
                        backgroundColor: `${BackgroundColor ? BackgroundColor : '#f1f1f7'} !important`,
                        // width: Width,
                        //borderRadius: Radius,
                        padding: `${Padding}px`,
                        // border: `${BorderSize}px solid ${BorderColor}`,
                        borderTop:
							 `${BorderSize}px solid ${BorderColor} !important`
								,
								borderRight:
								 `${BorderSize}px solid ${BorderColor} !important`
								,
								borderLeft:
						  `${BorderSize}px solid ${BorderColor} !important`,

					borderBottom:`${BorderSize}px solid ${BorderColor} !important`
							,
                        position: Position,
                        zIndex: zindex,
                    }}
                >
                    <Box
                        flex={1}
                        sx={InnerWrapper}
                    >

                        {htmlSnippet.includes("<") && htmlSnippet.includes(">") ? (
                            <Box
                                sx={{ marginTop: 2 ,whiteSpace: "pre-wrap",
                                wordBreak: "break-word"}}
                                dangerouslySetInnerHTML={{ __html: htmlSnippet }}
                            />
                        ) : (
                            <Box  sx={{ whiteSpace: "pre-wrap",
                            wordBreak: "break-word" , margin: "0px"}}>{htmlSnippet}</Box>
                        )}
                        {Emoji && (
                            <Typography
                                component="span"
                                sx={{
                                    fontWeight: Bold ? "bold" : "normal",
                                    fontStyle: Italic ? "italic" : "normal",
                                    color: TextColor,
                                    padding: "5px 2px",
                                    textAlign: Alignment,
                                    marginTop: 1,
                                }}
                            >
                                {Emoji}
                            </Typography>
                        )}
                            {Banner?.GuideStep?.map((step:any, index:any) => (
                            <Box key={step.StepId} sx={{display:"flex",alignItems:"center",width:"100%"}}>
                                {step.TextFieldProperties?.map((textField:any) => (
                                    <Box
                                    key={textField.Id}
                                    sx={{
                                        color: textField.TextProperties?.TextColor || "inherit",
                                        textAlign: textField.Alignment || "center",
                                        width: "100%",
                                        margin: "0px",
                                        padding: "5px 2px",
                                        // mt: 1,
                                    }}
                                    >
                                        {textField.Hyperlink ? (
                                            <a
                                                href={textField.Hyperlink}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                style={{
                                                    color: textField.TextProperties?.TextColor || "inherit",
                                                    textDecoration: "underline",
                                                }}

                                                onClick={() => {
                                                    trackUserEngagement(
                                                        "hyperLink-click", // Event type (you can change if needed)
                                                        userData,
                                                        Banner, // Your guide data
                                                        browser,
                                                        version,
                                                        textField.Text,
                                                        stepData,
                                                        0, // Add the missing argument (e.g., timeSpentOnStep)
                                                        0  // Add the missing argument (e.g., additionalData)
                                                    );
                                                }}

                                            >
                                                <span dangerouslySetInnerHTML={{ __html: renderHtmlSnippet(textField.Text) }} />
                                            </a>
                                        ) : (

<Typography
    style={{
        fontSize: "14px",

    }}
    sx={{
        "& p": {
            margin: "0px !important",
        }
    }}
    dangerouslySetInnerHTML={{
        __html: `<span>${renderHtmlSnippet(textField.Text)}</span>`,
    }}
/>
                                        )}
                                    </Box>
                                ))}
                             {Array.isArray(CustomButton) &&
 CustomButton.some((button: any) => button.ButtonName && button.ButtonName.trim() !== "") && (
                                        <DialogActions
                                        sx={{
                                                padding: "0 !important",
                                                height: "40px",
                                            }}

     >
         {CustomButton.map((button: any, index: any) => (
             <Button
                 key={index}
                 onClick={() => handleButtonClick(button.ButtonAction,button?.ButtonName)}
                 variant="contained"
                 style={{
                     color: button.ButtonProperties?.ButtonTextColor || "#fff",
                     backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "#007bff",
                     borderColor: button.ButtonProperties?.ButtonBorderColor ? `1px solid ${ button.ButtonProperties?.ButtonBorderColor} !important` : "none",
                     borderStyle: button.ButtonProperties?.ButtonBorderColor ? "solid" : "none",
				     borderWidth: button.ButtonProperties?.ButtonBorderColor ? "2px" : "0",
                     margin: "2px 5px",
                     fontSize: button.ButtonProperties?.FontSize || 15,
                     width: button.ButtonProperties?.Width || "auto",
                     padding: "8px 12px",
                     textTransform: "none",
                     borderRadius: "20px",
                     lineHeight:"normal"
                 }}
                 sx={{
                     "&:hover": {
                         filter: "brightness(1.2)",
                     },
                 }}
             >
                 {button.ButtonName}
             </Button>
         ))}
     </DialogActions>
 )}

                                {/* Render Image if available */}
                                {image && (

    <Box
        component="a"
        href={Banner.GuideStep[0]?.ImageProperties?.Hyperlink || "#"}
        target="_blank"
        rel="noopener noreferrer"
        sx={{
            display: "block",
            textAlign: Banner.GuideStep[0]?.ImageProperties?.Alignment || "center",
        }}
    >
                                        <Box
                                            component="img"
                                            src={isBase64(image.Url) ? image.Url : image.Url}
                                            sx={{
                                                maxHeight: Banner.GuideStep[0]?.ImageProperties?.MaxImageHeight || "auto",
                                                padding: `${Banner.GuideStep[0]?.ImageProperties?.Padding?.Top || 0}px ${Banner.GuideStep[0]?.ImageProperties?.Padding?.Right || 0
                                                    }px ${Banner.GuideStep[0]?.ImageProperties?.Padding?.Bottom || 0}px ${Banner.GuideStep[0]?.ImageProperties?.Padding?.Left || 0
                                                    }px`,
                                                objectFit: Banner.GuideStep[0]?.ImageProperties?.UploadedImages?.[0]?.Fit || "contain",
                                                display: "block",
                                                margin: "0 auto",
                                            }}
                                        />
                                    </Box>
                                )}
                            </Box>
                        ))}

                   {isCloseDisabled && (
                        <CustomIconButton
                            sx={{
                                boxShadow: "rgba(0, 0, 0, 0.15) 0px 4px 8px",
					margin: "2px 5px",
					background: "#fff !important",
					border: "1px solid #ccc",
					zIndex:"999999",
						borderRadius: "50px",
					padding:"5px !important",
                            }}
                            onClick={handleCloseBanner}
                        >
                            <CloseIcon
                               sx={{zoom:"0.7",color:"#000"}}
                            />
                        </CustomIconButton>
                    )}
                    </Box>
                    </Box>
            )}
        </div>    );
};

export default Banner;