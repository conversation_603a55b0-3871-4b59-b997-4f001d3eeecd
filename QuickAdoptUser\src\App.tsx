import React, { useState } from 'react';
import { BrowserRouter } from 'react-router-dom'; // Import BrowserRouter
import Announcement from "./components/Announcement/Announcement";
import Banner from "./components/Banner/Banner";
import Hotspotview from './components/Hotspot/Hotspotview';
import TooltipUserview from './components/Tooltips/Tooltipuserview';
import TourUserView from './components/Tours/TourUserView';
import ChecklistLauncher from './components/Checklist/ChecklistLauncher';
import ChatBot from './components/ChatBot';
import { donaEnabled } from './service/APIService';

const App = () => {
  const [isFromAi, setIsFromAi] = useState(false);
  const [guide, setGuide] = useState<any>(null);
  const [isOpen, setIsOpen] = useState(false);
  const location =window.location.href;
  const hideChatBotPaths = ".quickadopt.in/settings/install";
  const shouldHideChatBot = location.includes(hideChatBotPaths);
  return (
    
    <BrowserRouter> {/* Wrap your app with <PERSON>rowser<PERSON>outer */}
      <div>
          <Banner />
        <Announcement />
        <Hotspotview />
        <TooltipUserview/>
        <TourUserView guide={guide} isFromAi={isFromAi} setGuide={setGuide} setIsFromAi={setIsFromAi} isOpen={isOpen} setIsOpen={setIsOpen}/>
        <ChecklistLauncher />
        
        { (donaEnabled == "true" || donaEnabled == "True") && !shouldHideChatBot && (<ChatBot guide={guide} isFromAi={isFromAi} setGuide={setGuide} setIsFromAi={setIsFromAi} isOpen={isOpen} setIsOpen={setIsOpen} />)}
      
      </div>
    </BrowserRouter>
  );
};

export default App;

