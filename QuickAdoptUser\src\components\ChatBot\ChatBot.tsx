import React, { useState, useEffect } from 'react';
import ChatButton from './ChatButton';
import ChatModal from './ChatModal';
import { createNewThread } from '../Services/AssistantService';
import { preloadVoices } from '../../services/TextToSpeechService';


interface Message {
  text: string;
  isUser: boolean;
  timestamp: Date;
  id?: string; // Unique identifier for each message
  guide: any;
  isStatusMessage?: boolean;
 // Flag for status messages
}

const ChatBot: React.FC<any> = ({guide,setGuide,isFromAi,setIsFromAi,isOpen,setIsOpen}:any) => {

  const [voicesLoaded, setVoicesLoaded] = useState(false);
  const [newThreadCreated, setNewThreadCreated] = useState(false);
  const [isWelcomeMessageShown, setWelcomeMessageShown] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isReWelcomeMessageShown, setReWelcomeMessageShown] = useState(false);

  // Preload ResponsiveVoice when component mounts
  const handleOpenChat = async () => {
    try {
      setIsOpen(true);

    } catch (error) {
      console.error("Failed to create thread:", error);
    }
  };


  const handleCloseChat = () => {
    setIsOpen(false);
  };

  return (
    <>
      {!isOpen ?
        <ChatButton onClick={handleOpenChat} />:
        <ChatModal
          open={isOpen}
          onClose={handleCloseChat}
          guide={guide}
          isFromAi={isFromAi}
          setGuide={setGuide}
          setIsFromAi={setIsFromAi}
          setIsOpen={setIsOpen}
          newThreadCreated={newThreadCreated}
          setNewThreadCreated={setNewThreadCreated}
          setWelcomeMessageShown={setWelcomeMessageShown}
          isWelcomeMessageShown={isWelcomeMessageShown}
          messages={messages}
          setMessages={setMessages}
          isReWelcomeMessageShown={isReWelcomeMessageShown}
          setReWelcomeMessageShown={setReWelcomeMessageShown}
        />
      }
    </>
  );
};

export default ChatBot;
